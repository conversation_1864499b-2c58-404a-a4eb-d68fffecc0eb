<?php
if (!defined('ABSPATH')) {
    exit;
}

if (!class_exists('WP_List_Table')) {
    require_once(ABSPATH . 'wp-admin/includes/class-wp-list-table.php');
}

class Room_List_Table extends WP_List_Table {
    public function __construct() {
        parent::__construct([
            'singular' => 'room',
            'plural'   => 'rooms',
            'ajax'     => false
        ]);
    }

    public function get_columns() {
        return [
            'cb'           => '<input type="checkbox" />',
            'id'           => 'ID',
            'room_info'    => '房间信息',
            'carrier'      => '所属载体',
            'status'       => '入驻状态',
            'area'         => '面积',
            'facilities'   => '设备设施',
            'created_at'   => '添加时间',
            'updated_at'   => '修改时间',
            'actions'      => '操作'
        ];
    }

    public function get_sortable_columns() {
        return [
            'id'         => ['id', true],
            'area'       => ['area', false],
            'created_at' => ['created_at', false],
            'updated_at' => ['updated_at', false]
        ];
    }

    protected function column_default($item, $column_name) {
        switch ($column_name) {
            case 'id':
                return $item['id'];
            case 'room_info':
                $title = sprintf(
                    '%s楼 - %s',
                    esc_html($item['floor']),
                    esc_html($item['room_number'])
                );

                // 添加行操作链接
                $actions = $this->get_row_actions($item);

                return sprintf(
                    '<strong>%1$s</strong>%2$s',
                    $title,
                    $this->row_actions($actions)
                );
            case 'carrier':
                return esc_html($item['carrier']);
            case 'status':
                $status_colors = [
                    'occupied' => '#ff4d4f',
                    'reserved' => '#faad14',
                    'vacant'   => '#52c41a'
                ];
                $status_texts = [
                    'occupied' => '已入驻',
                    'reserved' => '已预定',
                    'vacant'   => '空置'
                ];
                $color = isset($status_colors[$item['status']]) ? $status_colors[$item['status']] : '#666';

                // 添加快速编辑下拉框
                $select = sprintf(
                    '<select class="quick-edit-status" data-original="%s">
                        <option value="vacant" %s>空置</option>
                        <option value="reserved" %s>已预定</option>
                        <option value="occupied" %s>已入驻</option>
                    </select>',
                    esc_attr($item['status']),
                    selected($item['status'], 'vacant', false),
                    selected($item['status'], 'reserved', false),
                    selected($item['status'], 'occupied', false)
                );

                return sprintf(
                    '<span style="color: %s;">%s</span>%s',
                    esc_attr($color),
                    esc_html($status_texts[$item['status']] ?? $item['status']),
                    $select
                );
            case 'area':
                return sprintf(
                    '%.2f m²',
                    floatval($item['area'])
                );
            case 'facilities':
                $facilities = json_decode($item['facilities'], true);
                if (empty($facilities)) {
                    return '<span class="no-facilities">无</span>';
                }
                $facilities_list = is_array($facilities) ? implode(', ', $facilities) : '无';
                return sprintf(
                    '<span class="facilities-tooltip" title="%s">%s</span>',
                    esc_attr($facilities_list),
                    count($facilities) . ' 项设施'
                );
            case 'created_at':
            case 'updated_at':
                return mysql2date('Y-m-d H:i:s', $item[$column_name]);
            default:
                return print_r($item, true);
        }
    }

    protected function get_row_actions($item) {
        $actions = [];

        // 编辑链接
        $actions['edit'] = sprintf(
            '<a href="%s">编辑</a>',
            esc_url(add_query_arg([
                'page'   => 'room-management',
                'action' => 'edit',
                'id'     => $item['id']
            ], admin_url('admin.php')))
        );

        // 删除链接
        $actions['delete'] = sprintf(
            '<a href="#" class="delete-room" data-id="%d">删除</a>',
            $item['id']
        );

        return $actions;
    }

    protected function column_cb($item) {
        return sprintf(
            '<input type="checkbox" name="room_ids[]" value="%s" />',
            $item['id']
        );
    }

    protected function get_bulk_actions() {
        return [
            'delete'        => '删除',
            'set_vacant'    => '设为空置',
            'set_reserved'  => '设为已预定',
            'set_occupied'  => '设为已入驻'
        ];
    }

    public function prepare_items() {
        global $wpdb;
        $table_name = $wpdb->prefix . 'room_management';

        // 设置每页显示数量
        $per_page = 20;
        $current_page = $this->get_pagenum();

        // 处理搜索
        $search = isset($_REQUEST['s']) ? wp_unslash(trim($_REQUEST['s'])) : '';

        // 处理排序
        $allowed_orderby = ['id', 'area', 'created_at', 'updated_at'];
        $orderby = isset($_REQUEST['orderby']) && in_array($_REQUEST['orderby'], $allowed_orderby)
            ? $_REQUEST['orderby']
            : 'id';
        $order = isset($_REQUEST['order']) && in_array(strtoupper($_REQUEST['order']), ['ASC', 'DESC'])
            ? strtoupper($_REQUEST['order'])
            : 'DESC';

        // 处理筛选
        $where = [];

        if ($search) {
            $where[] = $wpdb->prepare(
                "(room_number LIKE %s OR floor LIKE %s OR carrier LIKE %s)",
                '%' . $wpdb->esc_like($search) . '%',
                '%' . $wpdb->esc_like($search) . '%',
                '%' . $wpdb->esc_like($search) . '%'
            );
        }

        if (!empty($_REQUEST['status'])) {
            $where[] = $wpdb->prepare("status = %s", $_REQUEST['status']);
        }

        if (!empty($_REQUEST['floor'])) {
            $where[] = $wpdb->prepare("floor = %s", $_REQUEST['floor']);
        }

        if (!empty($_REQUEST['carrier'])) {
            $where[] = $wpdb->prepare("carrier = %s", $_REQUEST['carrier']);
        }

        $where_clause = !empty($where) ? 'WHERE ' . implode(' AND ', $where) : '';

        // 获取总数
        $total_items = $wpdb->get_var("SELECT COUNT(*) FROM $table_name $where_clause");

        // 获取数据
        $sql = $wpdb->prepare(
            "SELECT * FROM $table_name $where_clause ORDER BY $orderby $order LIMIT %d OFFSET %d",
            $per_page,
            ($current_page - 1) * $per_page
        );

        // 添加调试信息
        error_log('Room List SQL Query: ' . $sql);
        $this->items = $wpdb->get_results($sql, ARRAY_A);
        error_log('Room List Query Results: ' . print_r($this->items, true));

        // 设置分页参数
        $this->set_pagination_args([
            'total_items' => $total_items,
            'per_page'    => $per_page,
            'total_pages' => ceil($total_items / $per_page)
        ]);
    }

    protected function get_views() {
        global $wpdb;
        $table_name = $wpdb->prefix . 'room_management';

        $status_counts = $wpdb->get_results(
            "SELECT status, COUNT(*) as count FROM $table_name GROUP BY status",
            ARRAY_A
        );

        $views = [];
        $current = isset($_REQUEST['status']) ? $_REQUEST['status'] : 'all';

        // 计算总数
        $total_count = array_sum(array_column($status_counts, 'count'));

        // 添加"全部"视图
        $class = ($current === 'all' || empty($current)) ? ' class="current"' : '';
        $all_url = remove_query_arg('status');
        $views['all'] = sprintf(
            '<a href="%s"%s>全部 <span class="count">(%d)</span></a>',
            esc_url($all_url),
            $class,
            $total_count
        );

        // 状态映射
        $status_texts = [
            'vacant'   => '空置',
            'reserved' => '已预定',
            'occupied' => '已入驻'
        ];

        // 添加各状态视图
        foreach ($status_counts as $row) {
            if (isset($status_texts[$row['status']])) {
                $status = $row['status'];
                $text = $status_texts[$status];
                $count = $row['count'];

                $class = ($current === $status) ? ' class="current"' : '';
                $url = add_query_arg('status', $status);

                $views[$status] = sprintf(
                    '<a href="%s"%s>%s <span class="count">(%d)</span></a>',
                    esc_url($url),
                    $class,
                    $text,
                    $count
                );
            }
        }

        return $views;
    }

    public function display_rows() {
        $records = $this->items;

        list($columns, $hidden, $sortable, $primary) = $this->get_column_info();

        foreach ($records as $item) {
            echo '<tr id="room-' . $item['id'] . '" data-id="' . $item['id'] . '">';

            foreach ($columns as $column_name => $column_display_name) {
                $classes = "$column_name column-$column_name";
                if ($primary === $column_name) {
                    $classes .= ' has-row-actions column-primary';
                }

                if (in_array($column_name, $hidden)) {
                    $classes .= ' hidden';
                }

                echo '<td class="' . esc_attr($classes) . '">';

                echo $this->column_default($item, $column_name);

                if ($primary === $column_name) {
                    echo '<button type="button" class="toggle-row"><span class="screen-reader-text">显示详情</span></button>';
                }

                echo '</td>';
            }

            echo '</tr>';
        }
    }

    protected function handle_row_actions($item, $column_name, $primary) {
        if ($primary !== $column_name) {
            return '';
        }

        $actions = $this->get_row_actions($item);
        return $actions ? '<div class="row-actions">' . $this->row_actions($actions) . '</div>' : '';
    }
}
