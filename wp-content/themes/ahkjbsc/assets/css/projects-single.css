/* Single Project Page Styles */

.project-single {
    background: #f5f5f5;
    min-height: 100vh;
}

/* 面包屑导航 */
.project-breadcrumb {
    background: #fff;
    padding: 16px 0;
    border-bottom: 1px solid #f0f0f0;
}

/* 内容区域 */
.project-content-wrapper {
    padding: 40px 0;
}

.project-main {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    overflow: hidden;
}

/* 项目头部 */
.project-header {
    padding: 32px;
    border-bottom: 1px solid #f0f0f0;
    background: linear-gradient(135deg, #f0f8ff 0%, #fff 100%);
}

.project-basic-info {
    display: flex;
    align-items: center;
    gap: 24px;
}

.project-logo {
    flex-shrink: 0;
    width: 120px;
    height: 120px;
    border-radius: 12px;
    overflow: hidden;
    background: #fff;
    border: 2px solid #f0f0f0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.logo-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.project-info {
    flex: 1;
    min-width: 0;
}

.project-title {
    font-size: 28px;
    font-weight: 700;
    color: rgba(0, 0, 0, 0.85);
    margin: 0 0 16px 0;
    line-height: 1.3;
}

.project-categories {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.category-badge {
    padding: 6px 12px;
    background: #1890ff;
    color: #fff;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 500;
}

.project-meta-info {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.meta-item {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.65);
}

.meta-item .anticon {
    margin-right: 8px;
    color: #1890ff;
    font-size: 16px;
}

.meta-label {
    margin-right: 6px;
    color: rgba(0, 0, 0, 0.45);
    font-weight: 500;
}

.meta-value {
    color: rgba(0, 0, 0, 0.85);
    font-weight: 600;
}

/* 项目状态样式 */
.status-进行中 {
    color: #52c41a;
}

.status-已完成 {
    color: #1890ff;
}

.status-暂停 {
    color: #faad14;
}

.status-已取消 {
    color: #f5222d;
}

/* 内容区域 */
.project-content {
    padding: 32px;
}

.content-section {
    margin-bottom: 32px;
}

.content-section:last-child {
    margin-bottom: 0;
}

.section-title {
    font-size: 20px;
    font-weight: 600;
    color: rgba(0, 0, 0, 0.85);
    margin: 0 0 20px 0;
    display: flex;
    align-items: center;
    gap: 8px;
    padding-bottom: 12px;
    border-bottom: 2px solid #1890ff;
}

.section-title .anticon {
    color: #1890ff;
    font-size: 18px;
}

.section-content {
    color: rgba(0, 0, 0, 0.85);
    line-height: 1.8;
    font-size: 15px;
}

.section-content p {
    margin-bottom: 16px;
}

.section-content img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    margin: 16px 0;
}

/* 信息网格 */
.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(20px, 1fr));
    gap: 16px;
}

.info-item {
    padding: 16px;
    background: #fafafa;
    border-radius: 8px;
    border-left: 4px solid #1890ff;
}

.info-label {
    font-weight: 600;
    color: rgba(0, 0, 0, 0.85);
    margin-bottom: 4px;
    font-size: 15px;
}

.info-value {
    color: rgba(0, 0, 0, 0.65);
    font-size: 16px;
}

/* 侧边栏 */
.project-sidebar {
    padding-left: 24px;
}

.sidebar-widget {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    padding: 24px;
    margin-bottom: 24px;
    border: 1px solid #f0f0f0;
}

.widget-title {
    margin: 0 0 20px 0;
    font-size: 16px;
    font-weight: 600;
    color: rgba(0, 0, 0, 0.85);
    border-bottom: 2px solid #1890ff;
    padding-bottom: 8px;
}

/* 相关项目 */
.related-projects {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.related-project-item {
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 16px;
}

.related-project-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.related-project-content {
    display: flex;
    align-items: flex-start;
    gap: 12px;
}

.related-project-thumb {
    flex-shrink: 0;
    width: 50px;
    height: 50px;
    border-radius: 6px;
    overflow: hidden;
    background: #fafafa;
}

.related-thumb {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.related-project-info {
    flex: 1;
    min-width: 0;
}

.related-project-title {
    margin: 0 0 6px 0;
    font-size: 13px;
    line-height: 1.4;
}

.related-project-title a {
    color: rgba(0, 0, 0, 0.85);
    text-decoration: none;
    transition: color 0.3s;
}

.related-project-title a:hover {
    color: #1890ff;
}

.related-project-meta {
    font-size: 11px;
    color: rgba(0, 0, 0, 0.45);
}

.no-related {
    text-align: center;
    color: rgba(0, 0, 0, 0.45);
    font-style: italic;
    margin: 0;
}

/* 统计信息 */
.stats-info {
    display: flex;
    justify-content: space-between;
    text-align: center;
}

.stat-item {
    flex: 1;
}

.stat-number {
    font-size: 24px;
    font-weight: 700;
    color: #1890ff;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 12px;
    color: rgba(0, 0, 0, 0.45);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .project-content-wrapper {
        padding: 20px 0;
    }

    .project-header {
        padding: 20px;
    }

    .project-basic-info {
        flex-direction: column;
        align-items: center;
        text-align: center;
        gap: 16px;
    }

    .project-logo {
        width: 100px;
        height: 100px;
    }

    .project-title {
        font-size: 22px;
    }

    .project-content {
        padding: 20px;
    }

    .section-title {
        font-size: 18px;
    }

    .info-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .project-sidebar {
        padding-left: 0;
        margin-top: 20px;
    }

    .sidebar-widget {
        margin: 0 20px 20px 20px;
        padding: 16px;
    }

    .stats-info {
        flex-direction: column;
        gap: 16px;
    }

    .stat-item {
        padding: 12px;
        background: #fafafa;
        border-radius: 8px;
    }
}

@media (max-width: 480px) {
    .project-header {
        padding: 16px;
    }

    .project-title {
        font-size: 20px;
    }

    .project-content {
        padding: 16px;
    }

    .section-title {
        font-size: 16px;
    }

    .sidebar-widget {
        margin: 0 16px 16px 16px;
        padding: 12px;
    }

    .related-project-content {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }

    .related-project-thumb {
        width: 60px;
        height: 60px;
    }
}

/* 动画效果 */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.project-main > * {
    animation: slideInUp 0.6s ease-out;
}

.sidebar-widget {
    animation: slideInUp 0.6s ease-out;
}

/* 悬停效果 */
.sidebar-widget:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.related-project-item:hover {
    background: #f0f8ff;
    border-radius: 6px;
    padding: 8px;
    margin: -8px;
    transition: all 0.3s ease;
}
