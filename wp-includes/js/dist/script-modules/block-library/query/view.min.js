import*as e from"@wordpress/interactivity";var t={438:e=>{e.exports=import("@wordpress/interactivity-router")}},r={};function o(e){var n=r[e];if(void 0!==n)return n.exports;var i=r[e]={exports:{}};return t[e](i,i.exports,o),i.exports}o.d=(e,t)=>{for(var r in t)o.o(t,r)&&!o.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},o.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);const n=(e=>{var t={};return o.d(t,e),t})({getContext:()=>e.getContext,getElement:()=>e.getElement,store:()=>e.store,withSyncEvent:()=>e.withSyncEvent}),i=e=>e&&e instanceof window.HTMLAnchorElement&&e.href&&(!e.target||"_self"===e.target)&&e.origin===window.location.origin;(0,n.store)("core/query",{actions:{navigate:(0,n.withSyncEvent)((function*(e){const t=(0,n.getContext)(),{ref:r}=(0,n.getElement)(),s=r.closest(".wp-block-query[data-wp-router-region]");if(i(r)&&(e=>!(0!==e.button||e.metaKey||e.ctrlKey||e.altKey||e.shiftKey||e.defaultPrevented))(e)){e.preventDefault();const{actions:n}=yield Promise.resolve().then(o.bind(o,438));yield n.navigate(r.href),t.url=r.href;const i=".wp-block-post-template a[href]";s.querySelector(i)?.focus()}})),*prefetch(){const{ref:e}=(0,n.getElement)();if(i(e)){const{actions:t}=yield Promise.resolve().then(o.bind(o,438));yield t.prefetch(e.href)}}},callbacks:{*prefetch(){const{url:e}=(0,n.getContext)(),{ref:t}=(0,n.getElement)();if(e&&i(t)){const{actions:e}=yield Promise.resolve().then(o.bind(o,438));yield e.prefetch(t.href)}}}},{lock:!0});