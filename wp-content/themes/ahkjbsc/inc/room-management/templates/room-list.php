<?php
if (!defined('ABSPATH')) {
    exit;
}

// 显示消息
if (isset($success_message)): ?>
    <div class="notice notice-success is-dismissible">
        <p><?php echo esc_html($success_message); ?></p>
    </div>
<?php endif; ?>

<?php if (isset($error_message)): ?>
    <div class="notice notice-error is-dismissible">
        <p><?php echo esc_html($error_message); ?></p>
    </div>
<?php endif; ?>

<div class="wrap">
    <h1 class="wp-heading-inline">房屋管理</h1>
    <a href="<?php echo esc_url(add_query_arg('action', 'add')); ?>" class="page-title-action">添加房间</a>

    <hr class="wp-header-end">

    <!-- 搜索和筛选表单 -->
    <form method="get">
        <input type="hidden" name="page" value="room-management">

        <div class="tablenav top">
            <!-- 搜索框 -->
            <div class="alignleft actions">
                <label class="screen-reader-text" for="room-search-input">搜索房间:</label>
                <input type="search" id="room-search-input" name="s" value="<?php echo isset($_GET['s']) ? esc_attr($_GET['s']) : ''; ?>">
            </div>

            <!-- 筛选下拉框 -->
            <div class="alignleft actions">
                <!-- 楼层筛选 -->
                <select name="floor" id="filter-by-floor">
                    <option value="">所有楼层</option>
                    <?php
                    $floors = get_all_floors();
                    $current_floor = isset($_GET['floor']) ? $_GET['floor'] : '';
                    foreach ($floors as $floor) {
                        printf(
                            '<option value="%s" %s>%s</option>',
                            esc_attr($floor),
                            selected($current_floor, $floor, false),
                            esc_html($floor)
                        );
                    }
                    ?>
                </select>

                <!-- 载体筛选 -->
                <select name="carrier" id="filter-by-carrier">
                    <option value="">所有载体</option>
                    <?php
                    $carriers = get_all_carriers();
                    $current_carrier = isset($_GET['carrier']) ? $_GET['carrier'] : '';
                    foreach ($carriers as $carrier) {
                        printf(
                            '<option value="%s" %s>%s</option>',
                            esc_attr($carrier),
                            selected($current_carrier, $carrier, false),
                            esc_html($carrier)
                        );
                    }
                    ?>
                </select>

                <?php submit_button('筛选', '', 'filter_action', false); ?>
            </div>
        </div>
    </form>

    <!-- 房间列表表格 -->
    <form method="post" id="rooms-filter">
        <?php
        // 安全字段
        wp_nonce_field('room_bulk_action', 'room_bulk_nonce');
        
        // 创建表格实例
        $list_table = new Room_List_Table();
        
        // 显示状态筛选链接
        $list_table->views();
        
        // 准备项目
        $list_table->prepare_items();
        
        // 显示表格
        $list_table->display();
        ?>
    </form>
    
    <style>
        .column-id { width: 5%; }
        .column-room_info { width: 15%; }
        .column-carrier { width: 10%; }
        .column-status { width: 10%; }
        .column-area { width: 8%; }
        .column-facilities { width: 12%; }
        .column-created_at, .column-updated_at { width: 12%; }
        .column-actions { width: 10%; }
        
        .facilities-tooltip {
            cursor: help;
            border-bottom: 1px dotted #666;
        }
        
        .quick-edit-status {
            display: none;
        }
        
        .inline-edit-row .inline-edit-col {
            padding: 15px;
        }
    </style>
</div>

<script type="text/template" id="tmpl-room-quick-edit">
    <tr id="quick-edit-row-{{data.id}}" class="quick-edit-row inline-edit-row inline-edit-row-room quick-edit-row-room" style="display: none">
        <td colspan="<?php echo $list_table->get_column_count(); ?>" class="colspanchange">
            <fieldset class="inline-edit-col">
                <legend class="inline-edit-legend">快速编辑</legend>
                <div class="inline-edit-col">
                    <label>
                        <span class="title">状态</span>
                        <select name="status">
                            <option value="vacant">空置</option>
                            <option value="reserved">已预定</option>
                            <option value="occupied">已入驻</option>
                        </select>
                    </label>
                </div>
            </fieldset>
            <p class="submit inline-edit-save">
                <button type="button" class="button button-primary save">更新</button>
                <button type="button" class="button cancel">取消</button>
            </p>
        </td>
    </tr>
</script>
