<?php
if (!defined('ABSPATH')) {
    exit;
}

// 添加房间
function add_room($data) {
    global $wpdb;

    // 数据验证
    $errors = validate_room_data($data);
    if (!empty($errors)) {
        return new WP_Error('validation_failed', '数据验证失败', ['errors' => $errors]);
    }

    // 检查房间号是否已存在
    $exists = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM {$wpdb->prefix}room_management WHERE room_number = %s",
        $data['room_number']
    ));

    if ($exists) {
        return new WP_Error('room_exists', '该房间号已存在');
    }

    // 准备数据
    $insert_data = [
        'floor' => $data['floor'],
        'room_number' => $data['room_number'],
        'carrier' => $data['carrier'],
        'status' => $data['status'],
        'area' => $data['area'],
        'facilities' => isset($data['facilities']) ? json_encode($data['facilities'], JSON_UNESCAPED_UNICODE) : '',
        'created_at' => current_time('mysql'),
        'updated_at' => current_time('mysql')
    ];

    // 插入数据
    $result = $wpdb->insert(
        $wpdb->prefix . 'room_management',
        $insert_data,
        ['%s', '%s', '%s', '%s', '%f', '%s', '%s', '%s']
    );

    if ($result === false) {
        return new WP_Error('insert_failed', '添加房间失败');
    }

    return $wpdb->insert_id;
}

// 更新房间
function update_room($id, $data) {
    global $wpdb;

    // 数据验证
    $errors = validate_room_data($data, $id);
    if (!empty($errors)) {
        return new WP_Error('validation_failed', '数据验证失败', ['errors' => $errors]);
    }

    // 检查房间号是否已存在（排除当前记录）
    $exists = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM {$wpdb->prefix}room_management WHERE room_number = %s AND id != %d",
        $data['room_number'],
        $id
    ));

    if ($exists) {
        return new WP_Error('room_exists', '该房间号已存在');
    }

    // 准备数据
    $update_data = [
        'floor' => $data['floor'],
        'room_number' => $data['room_number'],
        'carrier' => $data['carrier'],
        'status' => $data['status'],
        'area' => $data['area'],
        'facilities' => isset($data['facilities']) ? json_encode($data['facilities'], JSON_UNESCAPED_UNICODE) : '',
        'updated_at' => current_time('mysql')
    ];

    // 更新数据
    $result = $wpdb->update(
        $wpdb->prefix . 'room_management',
        $update_data,
        ['id' => $id],
        ['%s', '%s', '%s', '%s', '%f', '%s', '%s'],
        ['%d']
    );

    if ($result === false) {
        return new WP_Error('update_failed', '更新房间失败');
    }

    return true;
}

// 删除房间
function delete_room($id) {
    global $wpdb;

    $result = $wpdb->delete(
        $wpdb->prefix . 'room_management',
        ['id' => $id],
        ['%d']
    );

    if ($result === false) {
        return new WP_Error('delete_failed', '删除房间失败');
    }

    return true;
}

// 获取单个房间
function get_room($id) {
    global $wpdb;

    $room = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM {$wpdb->prefix}room_management WHERE id = %d",
        $id
    ), ARRAY_A);

    if ($room) {
        $room['facilities'] = json_decode($room['facilities'], true);
    }

    return $room;
}

// 数据验证
function validate_room_data($data, $id = null) {
    $errors = [];

    // 楼层验证
    if (empty($data['floor'])) {
        $errors['floor'] = '楼层不能为空';
    }

    // 房间号验证
    if (empty($data['room_number'])) {
        $errors['room_number'] = '房间号不能为空';
    }

    // 载体验证
    if (empty($data['carrier'])) {
        $errors['carrier'] = '所属载体不能为空';
    }

    // 状态验证
    $valid_statuses = ['occupied', 'reserved', 'vacant'];
    if (empty($data['status']) || !in_array($data['status'], $valid_statuses)) {
        $errors['status'] = '无效的入驻状态';
    }

    // 面积验证
    if (empty($data['area']) || !is_numeric($data['area']) || $data['area'] <= 0) {
        $errors['area'] = '请输入有效的面积';
    }

    // 设施验证（可选）
    if (!empty($data['facilities']) && !is_array($data['facilities'])) {
        $errors['facilities'] = '设备设施格式无效';
    }

    return $errors;
}

// 获取所有楼层
function get_all_floors() {
    global $wpdb;
    return $wpdb->get_col("SELECT DISTINCT floor FROM {$wpdb->prefix}room_management ORDER BY floor");
}

// 获取所有载体
function get_all_carriers() {
    global $wpdb;
    return $wpdb->get_col("SELECT DISTINCT carrier FROM {$wpdb->prefix}room_management ORDER BY carrier");
}

// 批量更新状态
function bulk_update_room_status($ids, $status) {
    global $wpdb;

    if (!is_array($ids) || empty($ids)) {
        return new WP_Error('invalid_ids', '无效的房间ID');
    }

    $valid_statuses = ['occupied', 'reserved', 'vacant'];
    if (!in_array($status, $valid_statuses)) {
        return new WP_Error('invalid_status', '无效的入驻状态');
    }

    $ids = array_map('intval', $ids);
    $ids_string = implode(',', $ids);

    $result = $wpdb->query($wpdb->prepare(
        "UPDATE {$wpdb->prefix}room_management SET status = %s, updated_at = %s WHERE id IN ($ids_string)",
        $status,
        current_time('mysql')
    ));

    if ($result === false) {
        return new WP_Error('update_failed', '批量更新状态失败');
    }

    return $result;
}

// 渲染房间管理页面

// 渲染主页面
function render_room_management_page() {
    // 检查权限
    if (!current_user_can('manage_options')) {
        wp_die('权限不足');
    }

    // 获取当前操作
    $action = isset($_GET['action']) ? sanitize_text_field($_GET['action']) : 'list';

    echo '<hr class="wp-header-end">';

    // 处理表单提交
    if (isset($_POST['submit']) && isset($_POST['room_form_nonce'])) {
        if (!wp_verify_nonce($_POST['room_form_nonce'], 'room_form')) {
            wp_die('安全验证失败');
        }

        $room_data = [
            'floor' => sanitize_text_field($_POST['floor']),
            'room_number' => sanitize_text_field($_POST['room_number']),
            'carrier' => sanitize_text_field($_POST['carrier']),
            'status' => sanitize_text_field($_POST['status']),
            'area' => floatval($_POST['area']),
            'facilities' => isset($_POST['facilities']) ? json_encode(array_map('sanitize_text_field', $_POST['facilities'])) : '[]'
        ];

        global $wpdb;
        $table_name = $wpdb->prefix . 'room_management';

        if ($action === 'add') {
            $room_data['created_at'] = current_time('mysql');
            $room_data['updated_at'] = current_time('mysql');

            $result = $wpdb->insert(
                $table_name,
                $room_data,
                ['%s', '%s', '%s', '%s', '%f', '%s', '%s', '%s']
            );

            if ($result) {
                wp_redirect(add_query_arg(['message' => 'added'], remove_query_arg('action')));
                exit;
            }
        } elseif ($action === 'edit' && isset($_GET['id'])) {
            $room_id = intval($_GET['id']);
            $room_data['updated_at'] = current_time('mysql');

            $result = $wpdb->update(
                $table_name,
                $room_data,
                ['id' => $room_id],
                ['%s', '%s', '%s', '%s', '%f', '%s', '%s'],
                ['%d']
            );

            if ($result !== false) {
                wp_redirect(add_query_arg(['message' => 'updated'], remove_query_arg(['action', 'id'])));
                exit;
            }
        }
    }

    // 显示消息
    if (isset($_GET['message'])) {
        $message = '';
        $type = 'success';

        switch ($_GET['message']) {
            case 'added':
                $message = '房间添加成功。';
                break;
            case 'updated':
                $message = '房间更新成功。';
                break;
            default:
                $type = 'error';
                $message = '操作失败。';
        }

        if ($message) {
            echo '<div class="notice notice-' . esc_attr($type) . ' is-dismissible"><p>' . esc_html($message) . '</p></div>';
        }
    }

    // 加载相应的模板
    switch ($action) {
        case 'add':
        case 'edit':
            require_once get_template_directory() . '/inc/room-management/templates/room-form.php';
            break;

        default:
            require_once get_template_directory() . '/inc/room-management/templates/room-list.php';
    }

    echo '</div>';
}

// 获取房间状态统计
function get_room_status_counts() {
    global $wpdb;
    $counts = $wpdb->get_results(
        "SELECT status, COUNT(*) as count 
        FROM {$wpdb->prefix}room_management 
        GROUP BY status",
        ARRAY_A
    );
    
    $status_counts = [
        'all' => 0,
        'vacant' => 0,
        'reserved' => 0,
        'occupied' => 0
    ];
    
    foreach ($counts as $count) {
        $status_counts[$count['status']] = (int)$count['count'];
        $status_counts['all'] += (int)$count['count'];
    }
    
    return $status_counts;
}

// 获取所有可用的设施
function get_available_facilities() {
    return [
        'wifi' => 'WiFi',
        'air_conditioning' => '空调',
        'heating' => '暖气',
        'projector' => '投影仪',
        'whiteboard' => '白板',
        'desk' => '办公桌',
        'chair' => '办公椅',
        'cabinet' => '文件柜',
        'water_dispenser' => '饮水机',
        'power_socket' => '电源插座',
        'network_port' => '网络接口',
        'printer' => '打印机',
        'meeting_room' => '会议室',
        'rest_area' => '休息区',
        'parking' => '停车位'
    ];
}

// 添加管理菜单
add_action('admin_menu', function() {
    add_menu_page(
        '房屋管理',
        '房屋管理',
        'manage_options',
        'room-management',
        'render_room_management_page',
        'dashicons-building',
        30
    );
});

// 添加页面加载时的JavaScript
function room_management_admin_scripts() {
    $screen = get_current_screen();
    if ($screen && $screen->id === 'toplevel_page_room-management') {
        wp_enqueue_style('wp-jquery-ui-dialog');
        wp_enqueue_script('jquery-ui-dialog');
        wp_enqueue_script('jquery-ui-tooltip');
        
        wp_enqueue_script(
            'room-management',
            get_template_directory_uri() . '/inc/room-management/js/room-management.js',
            ['jquery', 'jquery-ui-dialog', 'jquery-ui-tooltip'],
            '1.0.0',
            true
        );

        wp_localize_script('room-management', 'roomManagement', [
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('room_management_nonce'),
            'messages' => [
                'confirmDelete' => '确定要删除选中的房间吗？',
                'confirmStatusUpdate' => '确定要更新选中房间的状态吗？',
                'deleteSuccess' => '删除成功',
                'updateSuccess' => '更新成功',
                'error' => '操作失败，请重试'
            ]
        ]);
        
        // 添加自定义样式
        wp_add_inline_style('wp-admin', '
            .wp-list-table .column-id { width: 5%; }
            .wp-list-table .column-room_info { width: 15%; }
            .wp-list-table .column-carrier { width: 10%; }
            .wp-list-table .column-status { width: 10%; }
            .wp-list-table .column-area { width: 8%; }
            .wp-list-table .column-facilities { width: 12%; }
            .wp-list-table .column-created_at,
            .wp-list-table .column-updated_at { width: 12%; }
            .wp-list-table .column-actions { width: 10%; }
            
            .quick-edit-status {
                display: none;
                margin-top: 5px;
            }
            
            .facilities-tooltip {
                cursor: help;
                border-bottom: 1px dotted #666;
            }
            
            .status-vacant { color: #52c41a; }
            .status-reserved { color: #faad14; }
            .status-occupied { color: #ff4d4f; }
            
            .room-form-wrap {
                max-width: 800px;
                margin: 20px 0;
                background: #fff;
                padding: 20px;
                box-shadow: 0 1px 1px rgba(0,0,0,.04);
            }
            
            .room-form-wrap .form-table th {
                width: 200px;
            }
            
            .room-facilities-wrap {
                display: grid;
                grid-template-columns: repeat(3, 1fr);
                gap: 10px;
                margin: 10px 0;
            }
            
            .room-facilities-wrap label {
                display: inline-block;
                margin-right: 15px;
            }
        ');
    }
}
add_action('admin_enqueue_scripts', 'room_management_admin_scripts');
