<?php
/**
 * Single project post template
 * 
 * @package ahkjbsc
 */

get_header();
?>

<?php if (have_posts()) : while (have_posts()) : the_post(); ?>

<div class="project-single">
    <!-- 面包屑导航 -->
    <div class="project-breadcrumb">
        <div class="container">
            <div class="ant-breadcrumb">
                <span class="ant-breadcrumb-link">
                    <a href="<?php echo home_url(); ?>">首页</a>
                </span>
                <span class="ant-breadcrumb-separator">/</span>
                <span class="ant-breadcrumb-link">
                    <a href="<?php echo home_url('projects'); ?>">项目概述</a>
                </span>
                <span class="ant-breadcrumb-separator">/</span>
                <span class="ant-breadcrumb-link">
                    <span>项目详情</span>
                </span>
            </div>
        </div>
    </div>

    <!-- 项目详情内容 -->
    <div class="project-content-wrapper">
        <div class="container">
            <div class="ant-row">
                <!-- 主内容区域 -->
                <div class="ant-col ant-col-18">
                    <div class="project-main">
                        <!-- 项目头部信息 -->
                        <header class="project-header">
                            <div class="project-basic-info">
                                <?php if (has_post_thumbnail()) : ?>
                                <div class="project-logo">
                                    <?php the_post_thumbnail('large', array('class' => 'logo-image')); ?>
                                </div>
                                <?php endif; ?>
                                
                                <div class="project-info">
                                    <h1 class="project-title"><?php the_title(); ?></h1>
                                    
                                    <?php 
                                    $project_categories = get_the_terms(get_the_ID(), 'project_category');
                                    if ($project_categories && !is_wp_error($project_categories)) :
                                    ?>
                                    <div class="project-categories">
                                        <?php foreach ($project_categories as $category) : ?>
                                        <span class="category-badge"><?php echo esc_html($category->name); ?></span>
                                        <?php endforeach; ?>
                                    </div>
                                    <?php endif; ?>
                                    
                                    <div class="project-meta-info">

                                    </div>
                                </div>
                            </div>
                        </header>
                        
                        <!-- 项目详细内容 -->
                        <div class="project-content">
                            <?php if (get_the_content()) : ?>
                            <div class="content-section">
                                <h3 class="section-title">
                                    <i class="anticon anticon-file-text"></i>
                                    项目介绍
                                </h3>
                                <div class="section-content">
                                    <?php the_content(); ?>
                                </div>
                            </div>
                            <?php endif; ?>
                            <div class="content-section">
                                <h3 class="section-title">
                                    <i class="anticon anticon-info-circle"></i>
                                    详细信息
                                </h3>
                                <div class="section-content">
                                    <div class="info-grid">
                                        <?php
                                        $created_date = get_field('created_date');
                                        $entered_date = get_field('entered_date');
                                        $member_count = get_field('member_count');
                                        $is_featured = get_field('is_featured');
                                        ?>

                                        <?php if ($created_date) : ?>
                                            <div class="info-item">
                                                <div class="info-label">成立时间：</div>
                                                <div class="info-value"><?php echo date('Y年m月', strtotime($created_date)); ?></div>
                                            </div>
                                        <?php endif; ?>

                                        <?php if ($entered_date) : ?>
                                            <div class="info-item">
                                                <div class="info-label">入驻时间：</div>
                                                <div class="info-value"><?php echo date('Y年m月', strtotime($entered_date)); ?></div>
                                            </div>
                                        <?php endif; ?>

                                        <?php if ($member_count) : ?>
                                            <div class="info-item">
                                                <div class="info-label">企业人数：</div>
                                                <div class="info-value"><?php echo esc_html($member_count); ?></div>
                                            </div>
                                        <?php endif; ?>

                                        <?php if ($is_featured) : ?>
                                            <div class="info-item">
                                                <div class="info-label">特色项目：</div>
                                                <div class="info-value">是</div>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 侧边栏 -->
                <div class="ant-col ant-col-6">
                    <aside class="project-sidebar">
                        <!-- 相关项目 -->
                        <div class="sidebar-widget related-widget">
                            <h4 class="widget-title">相关项目</h4>
                            <div class="related-projects">
                                <?php
                                $related_args = array(
                                    'post_type' => 'projects',
                                    'posts_per_page' => 5,
                                    'post__not_in' => array(get_the_ID()),
                                    'orderby' => 'rand',
                                    'post_status' => 'publish'
                                );
                                
                                // 如果有项目分类，优先显示同类型项目
                                if ($project_categories && !is_wp_error($project_categories)) {
                                    $related_args['tax_query'] = array(
                                        array(
                                            'taxonomy' => 'project_category',
                                            'field'    => 'term_id',
                                            'terms'    => wp_list_pluck($project_categories, 'term_id'),
                                        ),
                                    );
                                }
                                
                                $related_projects = new WP_Query($related_args);
                                
                                if ($related_projects->have_posts()) :
                                    while ($related_projects->have_posts()) : $related_projects->the_post();
                                ?>
                                <div class="related-project-item">
                                    <div class="related-project-content">
                                        <?php if (has_post_thumbnail()) : ?>
                                        <div class="related-project-thumb">
                                            <a href="<?php the_permalink(); ?>">
                                                <?php the_post_thumbnail('thumbnail', array('class' => 'related-thumb')); ?>
                                            </a>
                                        </div>
                                        <?php endif; ?>
                                        <div class="related-project-info">
                                            <h6 class="related-project-title">
                                                <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                                            </h6>
                                            <div class="related-project-meta">
                                                <?php 
                                                $related_status = get_field('project_status');
                                                if ($related_status) :
                                                ?>
                                                <span class="related-meta"><?php echo $related_status; ?></span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <?php 
                                    endwhile;
                                    wp_reset_postdata();
                                else :
                                ?>
                                <p class="no-related">暂无相关项目</p>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <!-- 统计信息 -->
                        <div class="sidebar-widget stats-widget">
                            <h4 class="widget-title">项目统计</h4>
                            <div class="stats-info">
                                <?php
                                $total_projects = wp_count_posts('projects')->publish;
                                $total_categories = wp_count_terms('project_category');
                                $featured_projects = get_posts(array(
                                    'post_type' => 'projects',
                                    'meta_key' => 'is_featured',
                                    'meta_value' => '1',
                                    'post_status' => 'publish',
                                    'numberposts' => -1
                                ));
                                $featured_count = count($featured_projects);
                                ?>
                                <div class="stat-item">
                                    <div class="stat-number"><?php echo $total_projects; ?></div>
                                    <div class="stat-label">总项目数</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number"><?php echo $total_categories; ?></div>
                                    <div class="stat-label">项目分类</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number"><?php echo $featured_count; ?></div>
                                    <div class="stat-label">特色项目</div>
                                </div>
                            </div>
                        </div>
                    </aside>
                </div>
            </div>
        </div>
    </div>
</div>

<?php endwhile; endif; ?>

<?php
get_footer();
?>
