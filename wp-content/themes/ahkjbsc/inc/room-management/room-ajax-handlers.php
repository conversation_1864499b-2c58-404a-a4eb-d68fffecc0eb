<?php
if (!defined('ABSPATH')) {
    exit;
}

// 更新房间状态
function handle_update_room_status() {
    check_ajax_referer('room_management_nonce', 'nonce');

    if (!current_user_can('manage_options')) {
        wp_send_json_error(['message' => '权限不足']);
    }

    $room_id = isset($_POST['room_id']) ? intval($_POST['room_id']) : 0;
    $status = isset($_POST['status']) ? sanitize_text_field($_POST['status']) : '';

    if (!$room_id || !in_array($status, ['vacant', 'reserved', 'occupied'])) {
        wp_send_json_error(['message' => '参数无效']);
    }

    global $wpdb;
    $table_name = $wpdb->prefix . 'room_management';

    $result = $wpdb->update(
        $table_name,
        ['status' => $status, 'updated_at' => current_time('mysql')],
        ['id' => $room_id],
        ['%s', '%s'],
        ['%d']
    );

    if ($result === false) {
        wp_send_json_error(['message' => '更新失败']);
    }

    wp_send_json_success(['message' => '更新成功']);
}
add_action('wp_ajax_update_room_status', 'handle_update_room_status');

// 批量操作房间
function handle_bulk_action_rooms() {
    check_ajax_referer('room_management_nonce', 'nonce');

    if (!current_user_can('manage_options')) {
        wp_send_json_error(['message' => '权限不足']);
    }

    $action = isset($_POST['bulk_action']) ? sanitize_text_field($_POST['bulk_action']) : '';
    $room_ids = isset($_POST['room_ids']) ? array_map('intval', (array)$_POST['room_ids']) : [];

    if (empty($room_ids)) {
        wp_send_json_error(['message' => '请选择要操作的房间']);
    }

    global $wpdb;
    $table_name = $wpdb->prefix . 'room_management';

    // 开始事务
    $wpdb->query('START TRANSACTION');

    try {
        $success = true;

        switch ($action) {
            case 'delete':
                foreach ($room_ids as $room_id) {
                    $result = $wpdb->delete(
                        $table_name,
                        ['id' => $room_id],
                        ['%d']
                    );
                    if ($result === false) {
                        $success = false;
                        break;
                    }
                }
                break;

            case 'set_vacant':
            case 'set_reserved':
            case 'set_occupied':
                $status = str_replace('set_', '', $action);
                $placeholders = array_fill(0, count($room_ids), '%d');
                $query = $wpdb->prepare(
                    "UPDATE $table_name SET status = %s, updated_at = %s WHERE id IN (" . implode(',', $placeholders) . ")",
                    array_merge([$status, current_time('mysql')], $room_ids)
                );
                $result = $wpdb->query($query);
                if ($result === false) {
                    $success = false;
                }
                break;

            default:
                $success = false;
                break;
        }

        if ($success) {
            $wpdb->query('COMMIT');
            wp_send_json_success(['message' => '操作成功']);
        } else {
            $wpdb->query('ROLLBACK');
            wp_send_json_error(['message' => '操作失败']);
        }

    } catch (Exception $e) {
        $wpdb->query('ROLLBACK');
        wp_send_json_error(['message' => '操作出错：' . $e->getMessage()]);
    }
}
add_action('wp_ajax_bulk_action_rooms', 'handle_bulk_action_rooms');

// 删除单个房间
function handle_delete_room() {
    check_ajax_referer('room_management_nonce', 'nonce');

    if (!current_user_can('manage_options')) {
        wp_send_json_error(['message' => '权限不足']);
    }

    $room_id = isset($_POST['room_id']) ? intval($_POST['room_id']) : 0;

    if (!$room_id) {
        wp_send_json_error(['message' => '参数无效']);
    }

    global $wpdb;
    $table_name = $wpdb->prefix . 'room_management';

    $result = $wpdb->delete(
        $table_name,
        ['id' => $room_id],
        ['%d']
    );

    if ($result === false) {
        wp_send_json_error(['message' => '删除失败']);
    }

    wp_send_json_success(['message' => '删除成功']);
}
add_action('wp_ajax_delete_room', 'handle_delete_room');

// 获取房间详情
function handle_get_room_details() {
    check_ajax_referer('room_management_nonce', 'nonce');

    if (!current_user_can('manage_options')) {
        wp_send_json_error(['message' => '权限不足']);
    }

    $room_id = isset($_POST['room_id']) ? intval($_POST['room_id']) : 0;

    if (!$room_id) {
        wp_send_json_error(['message' => '参数无效']);
    }

    global $wpdb;
    $table_name = $wpdb->prefix . 'room_management';

    $room = $wpdb->get_row(
        $wpdb->prepare(
            "SELECT * FROM $table_name WHERE id = %d",
            $room_id
        ),
        ARRAY_A
    );

    if (!$room) {
        wp_send_json_error(['message' => '房间不存在']);
    }

    // 处理设施数据
    $room['facilities'] = json_decode($room['facilities'], true);

    wp_send_json_success(['room' => $room]);
}
add_action('wp_ajax_get_room_details', 'handle_get_room_details');
