jQuery(document).ready(function($) {
    // 快速编辑状态
    $('.column-status').on('click', '.quick-edit-status', function(e) {
        e.stopPropagation();
        const $select = $(this);
        const $statusText = $select.prev();
        const originalStatus = $select.data('original');

        // 显示下拉框
        $statusText.hide();
        $select.show().focus();

        // 处理选择
        $select.on('change blur', function() {
            const newStatus = $(this).val();
            if (newStatus !== originalStatus) {
                const roomId = $(this).closest('tr').data('id');
                updateRoomStatus(roomId, newStatus, $statusText, $select);
            } else {
                $select.hide();
                $statusText.show();
            }
        });
    });

    // 更新房间状态
    function updateRoomStatus(roomId, status, $statusText, $select) {
        $.ajax({
            url: roomManagement.ajaxUrl,
            type: 'POST',
            data: {
                action: 'update_room_status',
                room_id: roomId,
                status: status,
                nonce: roomManagement.nonce
            },
            success: function(response) {
                if (response.success) {
                    // 更新显示
                    const statusColors = {
                        'occupied': '#ff4d4f',
                        'reserved': '#faad14',
                        'vacant': '#52c41a'
                    };
                    const statusTexts = {
                        'occupied': '已入驻',
                        'reserved': '已预定',
                        'vacant': '空置'
                    };

                    $statusText.css('color', statusColors[status])
                             .text(statusTexts[status]);
                    $select.data('original', status)
                           .hide();
                    $statusText.show();

                    // 显示成功消息
                    showNotice(roomManagement.messages.updateSuccess, 'success');
                } else {
                    showNotice(response.data.message || roomManagement.messages.error, 'error');
                    // 恢复原始状态
                    $select.val($select.data('original'))
                           .hide();
                    $statusText.show();
                }
            },
            error: function() {
                showNotice(roomManagement.messages.error, 'error');
                // 恢复原始状态
                $select.val($select.data('original'))
                       .hide();
                $statusText.show();
            }
        });
    }

    // 批量操作
    $('#doaction, #doaction2').on('click', function(e) {
        e.preventDefault();
        const $button = $(this);
        const select = $button.prev('select');
        const action = select.val();

        if (action === '-1') return;

        const $form = $('#rooms-filter');
        const $checkedBoxes = $form.find('input[name="room_ids[]"]:checked');

        if ($checkedBoxes.length === 0) {
            showNotice('请选择要操作的房间', 'error');
            return;
        }

        let confirmMessage;
        if (action === 'delete') {
            confirmMessage = roomManagement.messages.confirmDelete;
        } else if (action.startsWith('set_')) {
            confirmMessage = roomManagement.messages.confirmStatusUpdate;
        }

        if (confirmMessage && !confirm(confirmMessage)) {
            return;
        }

        const roomIds = $checkedBoxes.map(function() {
            return $(this).val();
        }).get();

        $.ajax({
            url: roomManagement.ajaxUrl,
            type: 'POST',
            data: {
                action: 'bulk_action_rooms',
                bulk_action: action,
                room_ids: roomIds,
                nonce: roomManagement.nonce
            },
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    showNotice(response.data.message || roomManagement.messages.error, 'error');
                }
            },
            error: function() {
                showNotice(roomManagement.messages.error, 'error');
            }
        });
    });

    // 删除单个房间
    $('.delete-room').on('click', function(e) {
        e.preventDefault();
        const $link = $(this);
        const roomId = $link.data('id');

        if (!confirm(roomManagement.messages.confirmDelete)) {
            return;
        }

        $.ajax({
            url: roomManagement.ajaxUrl,
            type: 'POST',
            data: {
                action: 'delete_room',
                room_id: roomId,
                nonce: roomManagement.nonce
            },
            success: function(response) {
                if (response.success) {
                    $link.closest('tr').fadeOut(400, function() {
                        $(this).remove();
                        showNotice(roomManagement.messages.deleteSuccess, 'success');
                    });
                } else {
                    showNotice(response.data.message || roomManagement.messages.error, 'error');
                }
            },
            error: function() {
                showNotice(roomManagement.messages.error, 'error');
            }
        });
    });

    // 显示通知消息
    function showNotice(message, type = 'success') {
        const $notice = $('<div>')
            .addClass('notice notice-' + type)
            .addClass('is-dismissible')
            .append($('<p>').text(message))
            .append(
                $('<button>')
                    .attr('type', 'button')
                    .addClass('notice-dismiss')
                    .append($('<span>').addClass('screen-reader-text').text('关闭'))
            );

        // 移除现有通知
        $('.notice').remove();

        // 添加新通知
        $('#wpbody-content').prepend($notice);

        // 处理关闭按钮
        $notice.find('.notice-dismiss').on('click', function() {
            $notice.fadeOut(300, function() { $(this).remove(); });
        });

        // 自动消失
        if (type === 'success') {
            setTimeout(function() {
                $notice.fadeOut(300, function() { $(this).remove(); });
            }, 3000);
        }
    }

    // 设施提示框
    $('.facilities-tooltip').each(function() {
        $(this).tooltip({
            items: '[title]',
            content: function() {
                return $(this).attr('title');
            },
            position: {
                my: 'left center',
                at: 'right+10 center'
            }
        });
    });

    // 处理分页跳转
    $('#jump-page').on('keypress', function(e) {
        if (e.which === 13) {
            e.preventDefault();
            jumpToPage();
        }
    });

    window.jumpToPage = function() {
        const input = document.getElementById('jump-page');
        const page = parseInt(input.value, 10);
        const maxPage = parseInt(input.getAttribute('max'), 10);

        if (page > 0 && page <= maxPage) {
            const url = new URL(window.location.href);
            url.searchParams.set('paged', page);
            window.location.href = url.toString();
        } else {
            showNotice('请输入有效的页码', 'error');
        }
    };
});
